body {
  font-family: sans-serif;
  margin: 0;
  background-color: #f4f4f4;
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 600px;
  margin: 30px auto;
  padding: 30px;
  background-color: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

nav {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

nav h1 {
  font-size: 30px;
  padding-left: 1em;;
}

nav a {
  color: black;
  text-decoration: none;
}

nav h1:hover {
  color: #f5876b;
  transition: 0.3s all ease;
}

#navpart-2 {
  display: flex;
  align-items: center;
  gap: 0.5vw;
}

#navpart-2 a {
  text-decoration: none;
  color: black;
  font-size: 18px;
}

#navpart-2 h4 {
  margin: 0 7px;
  padding: 10px 15px;
}

#navpart-2 h4:hover {
  background-color: rgba(230, 229, 229, 0.418);
  border-radius: 50px;
  transition: 0.4s ease;
}

header {
  position: relative;
  text-align: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #ddd;
  margin-bottom: 30px;
}


header h1 {
  margin-bottom: 5px;
  color: #555;
}

header p {
  color: #777;
}

.authentication-contact-form-section {
  padding: 20px;
  background-color: #f9f9f9;
  border: 1px solid #eee;
  border-radius: 6px;
}

.authentication-contact-form-section h2 {
  color: #555;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 2px solid #ddd;
  padding-bottom: 8px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group textarea {
  width: calc(100% - 12px);
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 16px;
}

.form-group textarea {
  resize: vertical;
}

.submit-button {
  background-color: #007bff;
  color: white;
  padding: 12px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 18px;
  transition: background-color 0.3s ease;
}

.submit-button:hover {
  background-color: #0056b3;
}

.error-message {
  color: red;
  font-size: 0.9em;
  margin-top: 5px;
}

.success-message {
  color: green;
  font-weight: bold;
  margin-top: 10px;
}

footer {
  text-align: center;
  padding-top: 20px;
  padding-bottom: 20px;
  font-weight: 300;
  border-top: 1px solid #ddd;
  border-radius: 4px;
  margin-top: 30px;
  color: #777;
}