* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: monospace;
}

html,
body {
    height: 100%;
    width: 100%;
    /* background-color: black; */
}

nav {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

nav h1 {
    font-size: 30px;
    padding-left: 1em;;
}

nav a {
    color: rgb(92, 90, 90);
    text-decoration: none;
}

nav h1:hover {
    color: #f5876b;
    transition: 0.3s all ease;
}

#navpart-2 {
    display: flex;
    align-items: center;
    gap: 0.5vw;
}

#navpart-2 a {
    text-decoration: none;
    color:gray;
    font-size: 18px;
}

#navpart-2 h4 {
    margin: 0 7px;
    padding: 10px 15px;
}

#navpart-2 h4:hover {
    border-radius: 50px;
    transition: 0.5s ease;
}

#navpart-2 a:hover {
    color: rgb(7, 7, 7);
    text-shadow: rgb(245, 160, 160) 0px 0px 5px;
    font-size: 20px;
}

#categories {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    gap: 1vw;
    margin: 5px;
}

#categories a {
    text-decoration: none;
    color: rgb(73, 70, 70);
    font-size: 19px;
}

#categories a:hover {
    color: #030303;
    font-size: 20px;
    text-shadow: rgb(248, 142, 142) 0px 0px 5px;
    transition: 0.5s ease;
}
#image-container img {
    width: 100%;
    height: 250%;
    border-radius: 15px;
}

#image-container {
    background-color: transparent;
    column-count: 5;
    column-gap: 15px;
    margin: 5px 5px;
}

footer {
    padding-top: 50px;
}

.container {
    width: 1148px;
    margin: auto;
    display: flex;
    justify-content: center;
}

.footer-content {
    width: 33.3%;
}

h3 {
    font-size: 28px;
    margin-bottom: 15px;
    text-align: center;
}

.footer-content i{
    font-size: 17px;
}

.footer-content p {
    width: 190px;
    color: rgb(95, 94, 94);
    font-size: 14px;
    font-weight: 550;
    margin: auto;
    padding: 7px;
}

.footer-content p:hover {
    color: #070707;
    transition: 0.5s ease;
}
.footer-content ul {
    text-align: center;
}

.list {
    padding: 0;
}

.list li {
    width: auto;
    text-align: center;
    list-style-type: none;
    padding: 7px;
    position: relative;
}

a {
    color: rgb(7, 7, 7);
    font-size: 15px;
}

.list li::before {
    content: '';
    position: absolute;
    transform: translate(-50%, -50%);
    left: 50%;
    top: 100%;
    width: 0;
    height: 2px;
    background: #f18930;
    transition-duration: .5s;
}

.list li:hover::before {
    width: 70px;
}

.social-icons li {
    display: inline-block;
    text-align: center;
    padding: 5px;
}

.social-icons i {
    color: rgb(7, 7, 7);
    font-size: 25px;
}

li a {
    text-decoration: none;
}

a:hover {
    color: #f18930;
}

.social-icons i:hover {
    color: #f18930;
}

.bottom-bar {
    text-align: center;
    padding: 10px 0;
    margin-top: 0px;
}

.bottom-bar p {
    color: rgb(61, 61, 61);
    margin: 0;
    font-size: 16px;
    padding: 7px;
}

.bottom-bar p:hover {
    color: black;
}